package com.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
// import org.springframework.stereotype.Component;

/**
 * GB28181配置属性类
 * 用于读取application.yml中的gb28181配置
 */
@Data
@ConfigurationProperties(prefix = "gb28181")
public class GB28181Properties {

    /**
     * 网络配置
     */
    private NetworkConfig network = new NetworkConfig();

    /**
     * SIP协议相关配置
     */
    private SipConfig sip = new SipConfig();

    /**
     * ZLMediaKit流媒体服务器配置
     */
    private ZlmConfig zlm = new ZlmConfig();
    
    /**
     * SIP配置内部类
     */
    @Data
    public static class SipConfig {
        /**
         * SIP信令端口
         * GB28181标准端口为15060
         */
        private int port = 15060;

        /**
         * SIP域
         * 通常是行政区划码前10位，5115250000
         */
        private String domain = "5115250000";

        /**
         * SIP服务器ID
         * 20位数字，符合GB28181规范
         * 格式：行政区划码(8位) + 行业编码(2位) + 类型编码(3位) + 网络标识(1位) + 序列号(6位)
         */
        private String id = "51152500002000000001";

        /**
         * 设备注册密码
         * 摄像机连接时需要配置相同的密码
         */
        private String password = "ybda1234";
        
        /**
         * 心跳间隔时间（秒）
         * 设备向服务器发送心跳的间隔
         */
        private int keepaliveInterval = 60;
        
        /**
         * 注册有效期（秒）
         * 设备注册后的有效时间
         */
        private int registerInterval = 3600;
    }
    
    /**
     * ZLMediaKit配置内部类
     */
    @Data
    public static class ZlmConfig {
        /**
         * ZLMediaKit服务器地址（将从network.zlmIp获取）
         */
        private String host;
        
        /**
         * ZLMediaKit HTTP API端口
         */
        private int port = 81;
        
        /**
         * ZLMediaKit API访问密钥
         * 用于调用ZLMediaKit的HTTP API
         */
        private String secret = "udgRe2yrs5aj4FVH4dE9P1Pd6Lbn2g0L";
        
        /**
         * RTP端口范围起始值
         * 用于分配给摄像机推流的端口
         */
        private int rtpPortStart = 30000;
        
        /**
         * RTP端口范围结束值
         */
        private int rtpPortEnd = 30500;
        
        /**
         * 获取ZLMediaKit的HTTP API基础URL
         */
        public String getApiBaseUrl() {
            String zlmHost = (host != null && !host.isEmpty()) ? host : "localhost";
            return String.format("http://%s:%d", zlmHost, port);
        }

        /**
         * 获取ZLMediaKit API基础URL（使用网络配置）
         *
         * @param networkConfig 网络配置
         * @return API基础URL
         */
        public String getApiBaseUrl(NetworkConfig networkConfig) {
            String zlmHost = networkConfig.getZlmIp();
            return String.format("http://%s:%d", zlmHost, port);
        }
    }

    /**
     * 网络配置内部类
     */
    @Data
    public static class NetworkConfig {
        /**
         * 服务器本地内网IP
         */
        private String localIp = "***************";

        /**
         * 服务器公网IP
         */
        private String publicIp = "***************";

        /**
         * SIP服务绑定IP (0.0.0.0表示绑定所有接口)
         */
        private String sipBindIp = "0.0.0.0";

        /**
         * SIP对外显示IP (Via/From头中使用)
         */
        private String sipExternalIp = "***************";

        /**
         * RTP媒体传输IP (SDP中使用)
         */
        private String mediaIp = "***************";

        /**
         * ZLMediaKit服务IP
         */
        private String zlmIp = "***************";
    }
}

-- GB28181设备表
CREATE TABLE IF NOT EXISTS gb28181_device (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    device_id VARCHAR(20) NOT NULL UNIQUE COMMENT '设备ID，20位数字',
    device_name VARCHAR(100) COMMENT '设备名称',
    manufacturer VARCHAR(50) COMMENT '设备厂商',
    model VARCHAR(50) COMMENT '设备型号',
    status VARCHAR(20) DEFAULT 'offline' COMMENT '设备状态：online-在线，offline-离线',
    ip_address VARCHAR(15) COMMENT '设备IP地址',
    port INT COMMENT '设备端口',
    last_register_time DATETIME COMMENT '最后注册时间',
    last_keepalive_time DATETIME COMMENT '最后心跳时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    server_ip VARCHAR(15) COMMENT '所属服务器IP（分布式部署时使用）',
    rtp_port INT COMMENT '当前分配的RTP端口',
    city VARCHAR(50) COMMENT '设备所在城市',
    county VARCHAR(50) COMMENT '设备所在县区',
    township VARCHAR(50) COMMENT '设备所在乡镇',
    
    INDEX idx_device_id (device_id),
    INDEX idx_status (status),
    INDEX idx_server_ip (server_ip),
    INDEX idx_location (city, county),
    INDEX idx_last_keepalive (last_keepalive_time)
) COMMENT 'GB28181设备信息表';

-- 设备服务器映射表（用于分布式部署）
CREATE TABLE IF NOT EXISTS device_server_mapping (
    device_id VARCHAR(20) PRIMARY KEY COMMENT '设备ID',
    server_ip VARCHAR(15) NOT NULL COMMENT '服务器IP地址',
    server_port INT DEFAULT 6625 COMMENT '服务器端口',
    rtp_port INT COMMENT '分配的RTP端口',
    city VARCHAR(50) COMMENT '城市',
    county VARCHAR(50) COMMENT '县区',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_server_ip (server_ip),
    INDEX idx_location (city, county)
) COMMENT '设备服务器映射表';

-- 插入一些测试数据（可选）
INSERT IGNORE INTO gb28181_device (device_id, device_name, manufacturer, model, status, city, county) VALUES
('12345678901234567890', '测试摄像机1', '海康威视', 'DS-2CD2T47G2-L', 'offline', '合肥市', '庐阳区'),
('12345678901234567891', '测试摄像机2', '大华', 'DH-IPC-HFW4433M-I2', 'offline', '合肥市', '蜀山区'),
('12345678901234567892', '测试摄像机3', '宇视', 'IPC2322LR3-SP-D', 'offline', '合肥市', '包河区');

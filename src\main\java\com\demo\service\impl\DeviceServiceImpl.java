package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.DTO.DeviceDTO;
import com.demo.entity.Device;
import com.demo.entity.DeviceStatusHistory;
import com.demo.mapper.DeviceMapper;
import com.demo.mapper.DeviceStatusHistoryMapper;
import com.demo.service.DeviceService;
import com.demo.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {
    @Autowired
    DeviceMapper deviceMapper;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    DeviceStatusHistoryMapper deviceStatusHistoryMapper;

    @Override
    public SaResult getDevice(DeviceDTO deviceDTO) {
        IPage<Device> page = new Page<>();
        page.setCurrent(deviceDTO.getCurPage());
        page.setSize(deviceDTO.getPageSize());
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(deviceDTO.getDeviceName()), "device_name", deviceDTO.getDeviceName());
        queryWrapper.like(StringUtils.isNotBlank(deviceDTO.getIp()), "ip", deviceDTO.getIp());
        queryWrapper.like(StringUtils.isNotBlank(deviceDTO.getEquipmentNumber()), "equipment_number", deviceDTO.getEquipmentNumber());
        queryWrapper.eq(StringUtils.isNotBlank(deviceDTO.getCity()), "city", deviceDTO.getCity());
        queryWrapper.eq(StringUtils.isNotBlank(deviceDTO.getCounty()), "county", deviceDTO.getCounty());
        queryWrapper.eq(StringUtils.isNotBlank(deviceDTO.getTownship()), "township", deviceDTO.getTownship());
        queryWrapper.eq(StringUtils.isNotBlank(deviceDTO.getHamlet()), "hamlet", deviceDTO.getHamlet());
        queryWrapper.eq(StringUtils.isNotBlank(deviceDTO.getSite()), "site", deviceDTO.getSite());
        queryWrapper.eq(deviceDTO.getState() != null, "state", deviceDTO.getState());
        queryWrapper.orderByDesc("create_time");
        return SaResult.data(deviceMapper.selectPage(page, queryWrapper));
    }

    @Override
    public SaResult addDevice(DeviceDTO deviceDTO) throws InvocationTargetException, IllegalAccessException {
        Device device = new Device();
        BeanUtils.copyProperties(device, deviceDTO);
        deviceMapper.insert(device);
        return SaResult.ok("添加成功");
    }

    @Override
    public SaResult updateDevice(Device device) {
        //QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("id", device.getId());
        //Device device1 = deviceMapper.selectOne(queryWrapper);
        //if (!device1.getIp().equals(device.getIp())) {
        //    QueryWrapper<Device> queryWrapper1 = new QueryWrapper<>();
        //    queryWrapper1.eq("ip", device.getIp());
        //    device1 = deviceMapper.selectOne(queryWrapper1);
        //    if (device1 != null) {
        //        return SaResult.error("该IP地址已存在");
        //    }
        //}
        int i = deviceMapper.updateById(device);
        if (i == 0) {
            return SaResult.error("修改失败");
        }
        return SaResult.ok("修改成功");
    }

    @Override
    public SaResult deviceCount() {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        Long currentCount = deviceMapper.selectCount(queryWrapper);

        Long previousCount = getPreviousDeviceCountFromRedis();

        int changePercentage = (int) compareDeviceCount(currentCount, previousCount);

        redisUtils.setEx("deviceCount", String.valueOf(currentCount), 1, TimeUnit.DAYS);

        return SaResult.data(Map.of("todayCount", currentCount, "increaseRate", changePercentage));
    }

    private Long getPreviousDeviceCountFromRedis() {
        Object count = redisUtils.get("deviceCount");
        return count != null ? Long.valueOf(count.toString()) : 0L;
    }

    private double compareDeviceCount(Long currentCount, Long previousCount) {
        if (previousCount == 0) {
            return 0.0;
        }
        return ((double) (currentCount - previousCount) / previousCount) * 100;
    }

    @Override
    public SaResult deviceOnline() {
        Double currentOnlineCount = deviceMapper.selectDeviceOnline();

        Double previousOnlineCount = getPreviousDeviceOnlineCountFromRedis();

        int onlineChangePercentage = (int) compareDeviceCount(currentOnlineCount, previousOnlineCount);

        redisUtils.setEx("deviceOnlineCount", String.valueOf(currentOnlineCount), 1, TimeUnit.DAYS);

        return SaResult.data(Map.of("todayOnlineCount", currentOnlineCount, "increaseRate", onlineChangePercentage));
    }

    @Override
    public SaResult getDeviceOverview(String city, String county, String township, String hamlet, String site) {
        try {
            // 查询所有设备
            QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotBlank(city), "city", city);
            queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
            queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
            queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
            queryWrapper.eq(StringUtils.isNotBlank(site), "site", site);
            List<Device> devices = deviceMapper.selectList(queryWrapper);
            // 按点位分组统计设备状态
            Map<String, List<Device>> devicesByLocation = devices.stream()
                    .collect(Collectors.groupingBy(device ->
                            String.join("|",
                                    StringUtils.defaultString(device.getCity(), ""),
                                    StringUtils.defaultString(device.getCounty(), ""),
                                    StringUtils.defaultString(device.getTownship(), ""),
                                    StringUtils.defaultString(device.getHamlet(), ""),
                                    StringUtils.defaultString(device.getSite(), "")
                            )
                    ));

            // 统计正常和异常点位
            List<Map<String, Object>> normalLocations = new ArrayList<>();
            List<Map<String, Object>> abnormalLocations = new ArrayList<>();

            devicesByLocation.forEach((location, locationDevices) -> {
                // 检查该点位是否所有设备都在线
                boolean isAllOnline = locationDevices.stream()
                        .allMatch(device -> device.getState() != null && device.getState() == 0);

                // 统计该点位的设备状态
                long onlineCount = locationDevices.stream()
                        .filter(device -> device.getState() != null && device.getState() == 0)
                        .count();
                long offlineCount = locationDevices.size() - onlineCount;

                // 构建点位信息
                Map<String, Object> locationInfo = new HashMap<>();
                String[] locationParts = location.split("\\|");
                locationInfo.put("city", locationParts[0]);
                locationInfo.put("county", locationParts[1]);
                locationInfo.put("township", locationParts[2]);
                locationInfo.put("hamlet", locationParts[3]);
                locationInfo.put("site", locationParts[4]);
                locationInfo.put("totalDevices", locationDevices.size());
                locationInfo.put("onlineDevices", onlineCount);
                locationInfo.put("offlineDevices", offlineCount);

                // 根据点位状态分类
                if (isAllOnline) {
                    normalLocations.add(locationInfo);
                } else {
                    abnormalLocations.add(locationInfo);
                }
            });

            // 构建最终结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalLocations", devicesByLocation.size());
            result.put("normalLocations", normalLocations);
            result.put("abnormalLocations", abnormalLocations);

            // 构建图表数据
            List<Map<String, Object>> types = new ArrayList<>();
            Map<String, Object> normalType = new HashMap<>();
            normalType.put("name", "正常点位");
            normalType.put("value", normalLocations.size());
            Map<String, Object> abnormalType = new HashMap<>();
            abnormalType.put("name", "异常点位");
            abnormalType.put("value", abnormalLocations.size());
            types.add(normalType);
            types.add(abnormalType);
            result.put("types", types);

            return SaResult.data(result);
        } catch (Exception e) {
            log.error("获取设备概览失败", e);
            return SaResult.error("获取设备概览失败：" + e.getMessage());
        }
    }

    private Double getPreviousDeviceOnlineCountFromRedis() {
        Object count = redisUtils.get("deviceOnlineCount");
        return count != null ? Double.valueOf(count.toString()) : 0.0;
    }

    private double compareDeviceCount(Double currentCount, Double previousCount) {
        if (previousCount == 0) {
            return 0.0;
        }
        return ((currentCount - previousCount) / previousCount) * 100;
    }

    @Override
    public SaResult getDevicesByLocation(String city, String county, String township,
                                         String hamlet, String site, Integer state) {
        try {
            QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotBlank(city), "city", city);
            queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
            queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
            queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
            queryWrapper.eq(StringUtils.isNotBlank(site), "site", site);
            queryWrapper.eq(state != null, "state", state);
            List<Device> devices = deviceMapper.selectList(queryWrapper);

            //  按地点分组处理
            Map<String, List<Map<String, Object>>> groupedDevices = devices.stream()
                    .collect(Collectors.groupingBy(
                            this::buildLocationKey,
                            Collectors.mapping(
                                    device -> {
                                        Map<String, Object> deviceInfo = new HashMap<>();
                                        deviceInfo.put("id", device.getId());
                                        deviceInfo.put("deviceName", device.getDeviceName());
                                        deviceInfo.put("deviceType", device.getDeviceType());
                                        deviceInfo.put("state", device.getState());
                                        deviceInfo.put("equipmentNumber", device.getEquipmentNumber());
                                        deviceInfo.put("longitude", device.getLongitude());
                                        deviceInfo.put("latitude", device.getLatitude());
                                        deviceInfo.put("streamKey", device.getStreamKey());
                                        deviceInfo.put("maintainerPhone", device.getMaintainerPhone());

                                        // 如果设备离线，添加离线时间
                                        if (device.getState() != null && device.getState() != 0) {
                                            Date offlineTime = device.getUpdateTime();
                                            if (offlineTime != null) {
                                                // 格式化离线时间
                                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                                deviceInfo.put("offlineTime", sdf.format(offlineTime));

                                                // 计算离线时长
                                                long diffInMillies = System.currentTimeMillis() - offlineTime.getTime();
                                                long days = TimeUnit.MILLISECONDS.toDays(diffInMillies);
                                                long hours = TimeUnit.MILLISECONDS.toHours(diffInMillies) % 24;
                                                long minutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillies) % 60;

                                                StringBuilder offlineDuration = new StringBuilder();
                                                if (days > 0) {
                                                    offlineDuration.append(days).append("天");
                                                }
                                                if (hours > 0) {
                                                    offlineDuration.append(hours).append("小时");
                                                }
                                                if (minutes > 0) {
                                                    offlineDuration.append(minutes).append("分钟");
                                                }
                                                if (offlineDuration.length() == 0) {
                                                    offlineDuration.append("刚刚离线");
                                                }
                                                deviceInfo.put("offlineDuration", offlineDuration.toString());
                                            }
                                        }

                                        return deviceInfo;
                                    },
                                    Collectors.toList()
                            )
                    ));

            // 转换为前端需要的格式
            List<Map<String, Object>> result = groupedDevices.entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> locationGroup = new HashMap<>();
                        String[] locationParts = entry.getKey().split("\\|");
                        locationGroup.put("city", locationParts[0]);
                        locationGroup.put("county", locationParts[1]);
                        locationGroup.put("township", locationParts[2]);
                        locationGroup.put("hamlet", locationParts[3]);
                        locationGroup.put("site", locationParts[4]);
                        locationGroup.put("devices", entry.getValue());
                        return locationGroup;
                    })
                    .collect(Collectors.toList());

            return SaResult.data(result);
        } catch (Exception e) {
            log.error("按地点查询设备失败", e);
            return SaResult.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public SaResult equipmentOnline(String equipmentNumber, Boolean equipmentOnline, String[] normal, String[] abnormal) {
        // 检查输入参数是否为空或为null
        if (normal == null || normal.length == 0 && (abnormal == null || abnormal.length == 0)) {
            return SaResult.error("请传递正确的参数"); // 如果两个数组都为空或为null，直接返回
        }

        Set<String> normalIps = toSet(normal);
        if (equipmentOnline) {
            processNormalIps(normalIps, equipmentNumber, 0);
            // 联动同点位下电脑设备为在线
            // 查询所有摄像机设备，获取其点位信息
            List<Device> cameras = deviceMapper.selectList(new QueryWrapper<Device>()
                .eq("equipment_number", equipmentNumber)
                .in("ip", normalIps)
                .eq("device_type", "摄像机"));
            if (!cameras.isEmpty()) {
                Device camera = cameras.get(0);
                // 查找同点位下所有电脑设备
                List<Device> computers = deviceMapper.selectList(new QueryWrapper<Device>()
                    .eq("city", camera.getCity())
                    .eq("county", camera.getCounty())
                    .eq("township", camera.getTownship())
                    .eq("hamlet", camera.getHamlet())
                    .eq("site", camera.getSite())
                    .eq("device_type", "电脑"));
                Date now = new Date();
                for (Device computer : computers) {
                    if (computer.getState() != 0) {
                        computer.setState(0);
                        computer.setLastOnlineTime(now);
                        deviceMapper.updateById(computer);
                        recordDeviceStatusChange(computer, 0, "摄像机上报在线联动电脑在线");
                        log.info("摄像机上报在线，联动电脑{}为在线", computer.getEquipmentNumber());
                    }else {
                        computer.setLastOnlineTime(now);
                        deviceMapper.updateById(computer);
                    }
                }
            }
        } else {
            Set<String> abnormalIps = toSet(abnormal);
            if (!abnormalIps.isEmpty()) {
                processAbnormalIps(abnormalIps, equipmentNumber, 1);
            }
            if (!normalIps.isEmpty()) {
                processNormalIps(normalIps, equipmentNumber, 0);
            }
        }

        return SaResult.ok("更新状态成功");
    }

    @Override
    public SaResult getsThePointDevice(String city, String county, String township, String hamlet, String site) {
        try {
            // 1. 查询设备
            QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotBlank(city), "city", city);
            queryWrapper.eq(StringUtils.isNotBlank(county), "county", county);
            queryWrapper.eq(StringUtils.isNotBlank(township), "township", township);
            queryWrapper.eq(StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
            queryWrapper.eq(StringUtils.isNotBlank(site), "site", site);
            List<Device> devices = deviceMapper.selectList(queryWrapper);
            // 2. 按地点分组处理设备
            Map<String, List<Map<String, Object>>> groupedDevices = devices.stream()
                    .collect(Collectors.groupingBy(
                            device -> buildLocationKey(device),
                            Collectors.mapping(
                                    device -> {
                                        Map<String, Object> deviceInfo = new HashMap<>();
                                        deviceInfo.put("id", device.getId());
                                        deviceInfo.put("name", device.getDeviceName());
                                        deviceInfo.put("type", device.getDeviceType());
                                        deviceInfo.put("status", device.getState());
                                        // 添加更多设备信息
                                        // deviceInfo.put("ip", device.getIp());
                                        deviceInfo.put("equipmentNumber", device.getEquipmentNumber());
                                        deviceInfo.put("longitude", device.getLongitude());
                                        deviceInfo.put("latitude", device.getLatitude());
                                        deviceInfo.put("streamKey", device.getStreamKey());
                                        // deviceInfo.put("createTime", device.getCreateTime());
                                        // deviceInfo.put("updateTime", device.getUpdateTime());
                                        // deviceInfo.put("lastOnlineTime", device.getLastOnlineTime());

                                        // // 如果设备离线，添加离线时间信息
                                        // if (device.getState() != null && device.getState() != 0) {
                                        //     Date offlineTime = device.getUpdateTime();
                                        //     if (offlineTime != null) {
                                        //         // 格式化离线时间
                                        //         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                        //         deviceInfo.put("offlineTime", sdf.format(offlineTime));

                                        //         // 计算离线时长
                                        //         long diffInMillies = System.currentTimeMillis() - offlineTime.getTime();
                                        //         long days = TimeUnit.MILLISECONDS.toDays(diffInMillies);
                                        //         long hours = TimeUnit.MILLISECONDS.toHours(diffInMillies) % 24;
                                        //         long minutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillies) % 60;

                                        //         StringBuilder offlineDuration = new StringBuilder();
                                        //         if (days > 0) {
                                        //             offlineDuration.append(days).append("天");
                                        //         }
                                        //         if (hours > 0) {
                                        //             offlineDuration.append(hours).append("小时");
                                        //         }
                                        //         if (minutes > 0) {
                                        //             offlineDuration.append(minutes).append("分钟");
                                        //         }
                                        //         if (offlineDuration.length() == 0) {
                                        //             offlineDuration.append("刚刚离线");
                                        //         }
                                        //         deviceInfo.put("offlineDuration", offlineDuration.toString());
                                        //     }
                                        // }
                                        return deviceInfo;
                                    },
                                    Collectors.toList()
                            )
                    ));

            // 3. 转换为前端需要的格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map.Entry<String, List<Map<String, Object>>> entry : groupedDevices.entrySet()) {
                String[] locationParts = entry.getKey().split("\\|");
                Map<String, Object> locationGroup = new HashMap<>();
                locationGroup.put("city", locationParts[0]);
                locationGroup.put("county", locationParts[1]);
                locationGroup.put("township", locationParts[2]);
                locationGroup.put("hamlet", locationParts[3]);
                locationGroup.put("site", locationParts[4]);
                locationGroup.put("devices", entry.getValue());
                result.add(locationGroup);
            }

            return SaResult.data(result);
        } catch (Exception e) {
            log.error("获取点位设备失败", e);
            return SaResult.error("获取点位设备失败: " + e.getMessage());
        }
    }

    @Override
    public SaResult getCameraUrl(String equipmentNumber) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", equipmentNumber);
        queryWrapper.eq("device_type", "摄像机");
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        return SaResult.data(devices);
    }

    @Override
    public SaResult getRoadCrossingDeviceConfig(String equipmentNumber) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", equipmentNumber);
        queryWrapper.eq("device_type", "电脑");
        Device device = deviceMapper.selectOne(queryWrapper);
        return SaResult.data(device);
    }

    @Override
    public String updateRoadCrossingDeviceConfig(String equipmentNumber) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", equipmentNumber);
        queryWrapper.eq("device_type", "电脑");
        Device device = deviceMapper.selectOne(queryWrapper);
        if (device != null) {
            Long updateTimeSeconds = device.getUpdateTime() == null ? null : device.getUpdateTime().getTime() / 1000;
            if (device.getUrgent() != null) {
                return device.getUrgent() + "," + updateTimeSeconds;
            }
        }
        return null; // 或者根据业务逻辑返回其他默认值
    }


    /**
     * 将数组转换为集合，并处理空值情况
     */
    private Set<String> toSet(String[] array) {
        if (array == null || array.length == 0) {
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.asList(array));
    }

    /**
     * 处理正常IP的状态更新
     */
    private void processNormalIps(Set<String> ips, String equipmentNumber, int toState) {
        if (ips.isEmpty()) {
            return;
        }
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", equipmentNumber)
                .in("ip", ips);
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        Date now = new Date();
        for (Device device : devices) {
            // 只在状态发生变化时更新
            if (device.getState() != toState) {
                device.setState(toState);
                device.setLastOnlineTime(now);
                deviceMapper.updateById(device);
                
                // 记录状态变化历史
                String reason = toState == 0 ? 
                    "设备主动上报在线状态" : 
                    "设备主动上报离线状态";
                recordDeviceStatusChange(device, toState, reason);
                log.info("设备{}状态已更新为{}", equipmentNumber, toState == 0 ? "在线" : "离线");
            }else {
                device.setLastOnlineTime(now);
                deviceMapper.updateById(device);
            }
        }
    }

    /**
     * 处理异常IP的状态更新
     */
    private void processAbnormalIps(Set<String> ips, String equipmentNumber, int toState) {
        if (ips.isEmpty()) {
            return;
        }
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_number", equipmentNumber)
                .in("ip", ips);
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        Date now = new Date();
        for (Device device : devices) {
            // 只在状态发生变化时更新
            if (device.getState() != toState) {
                device.setState(toState);
                device.setLastOnlineTime(now);
                deviceMapper.updateById(device);
                // 记录状态变化历史
                String reason = toState == 0 ? 
                    "设备主动上报在线状态" : 
                    "设备主动上报离线状态";
                recordDeviceStatusChange(device, toState, reason);
                log.info("设备{}状态已更新为{}", equipmentNumber, toState == 0 ? "在线" : "离线");
            }
        }
    }

    /**
     * 构建地点键
     */
    private String buildLocationKey(Device device) {
        return String.join("|",
                StringUtils.defaultString(device.getCity(), ""),
                StringUtils.defaultString(device.getCounty(), ""),
                StringUtils.defaultString(device.getTownship(), ""),
                StringUtils.defaultString(device.getHamlet(), ""),
                StringUtils.defaultString(device.getSite(), "")
        );
    }

    /**
     * 记录设备状态变化
     */
    @Override
    public void recordDeviceStatusChange(Device device, Integer newState, String reason) {
        Date now = new Date();
        
        // 1. 检查是否存在当前未结束的状态记录
        DeviceStatusHistory lastHistory = deviceStatusHistoryMapper.selectOne(
            new QueryWrapper<DeviceStatusHistory>()
                .eq("equipment_number", device.getEquipmentNumber())
                .eq("ip", device.getIp())
                .isNull("end_time")
                .orderByDesc("start_time")
                .last("LIMIT 1")
        );
        
        // 2. 如果存在未结束的记录且状态相同，则不做任何操作（保持连续性）
        if (lastHistory != null && Objects.equals(lastHistory.getState(), newState)) {
            log.debug("设备{}(IP:{})状态未发生变化，保持连续记录", device.getEquipmentNumber(), device.getIp());
            return;
        }
        
        // 3. 结束当前状态记录（如果存在）
        if (lastHistory != null) {
            lastHistory.setEndTime(now);
            if (lastHistory.getStartTime() != null) {
                lastHistory.setDuration((now.getTime() - lastHistory.getStartTime().getTime()) / 1000);
            }
            deviceStatusHistoryMapper.updateById(lastHistory);
            log.info("结束设备{}(IP:{})的状态记录，状态从{}变更为{}", 
                device.getEquipmentNumber(), device.getIp(), lastHistory.getState(), newState);
        }
        
        // 4. 创建新的状态记录
        DeviceStatusHistory history = new DeviceStatusHistory();
        history.setEquipmentNumber(device.getEquipmentNumber());
        history.setDeviceType(device.getDeviceType());
        history.setIp(device.getIp());
        history.setStartTime(now);
        history.setState(newState);
        history.setReason(reason);
        
        deviceStatusHistoryMapper.insert(history);
        log.info("创建设备{}(IP:{})新状态记录，状态：{}，原因：{}", 
            device.getEquipmentNumber(), device.getIp(), newState, reason);
    }

    @Override
    public boolean hasDeviceStatusHistory(String equipmentNumber, String ip) {
        Long count = deviceStatusHistoryMapper.selectCount(
            new QueryWrapper<DeviceStatusHistory>()
                .eq("equipment_number", equipmentNumber)
                .eq("ip", ip)
        );
        return count != null && count > 0;
    }

}

时间[10:35:34 150] 接收: REGISTER sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1533951720
From: <sip:34020000001320000001@3402000000>;tag=232164987
To: <sip:34020000001320000001@3402000000>
Call-ID: 2053376411
CSeq: 1 REGISTER
Contact: <sip:34020000001320000001@*************:5060>
Max-Forwards: 70
User-Agent: IP Camera
Expires: 3600
X-GB-Ver: 3.0
Content-Length: 0


时间[10:35:34 153] 解析: 注册认证失败


时间[10:35:34 154] 发送: SIP/2.0 401 Unauthorized
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1533951720
From: <sip:34020000001320000001@3402000000>;tag=232164987
To: <sip:34020000001320000001@3402000000>
Call-ID: 2053376411
CSeq: 1 REGISTER
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:34.150
Content-Length: 0
WWW-Authenticate: Digest realm="3402000000", algorithm=md5, nonce="c15d5cf6177e41649e89453dd5df1518"


时间[10:35:34 263] 接收: REGISTER sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1155847842
From: <sip:34020000001320000001@3402000000>;tag=232164987
To: <sip:34020000001320000001@3402000000>
Call-ID: 2053376411
CSeq: 2 REGISTER
Contact: <sip:34020000001320000001@*************:5060>
Authorization: Digest username="34020000001320000001", realm="3402000000", nonce="c15d5cf6177e41649e89453dd5df1518", uri="sip:34020000002000000001@3402000000", response="0fc2e5ff3b9d3ea208f2a390c55039df", algorithm=MD5
Max-Forwards: 70
User-Agent: IP Camera
Expires: 3600
X-GB-Ver: 3.0
Content-Length: 0


时间[10:35:34 270] 解析: 设备注册成功


时间[10:35:34 270] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1155847842
From: <sip:34020000001320000001@3402000000>;tag=232164987
To: <sip:34020000001320000001@3402000000>
Call-ID: 2053376411
CSeq: 2 REGISTER
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:34.262
Content-Length: 0


时间[10:35:34 271] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK5c8d41c2ffe644a4a29d3a3e230e9d87
From: <sip:34020000002000000001@**************:15060>;tag=1000
To: <sip:34020000001320000001@*************:52309>
Call-ID: 188ff6f804f343a685fbcb5ae52cead2
CSeq: 1 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 154
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>DeviceInfo</CmdType>
  <SN>10000</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:35:34 273] 解析: 获取设备信息


时间[10:35:34 476] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK5c8d41c2ffe644a4a29d3a3e230e9d87
From: <sip:34020000002000000001@**************:15060>;tag=1000
To: <sip:34020000001320000001@*************:52309>;tag=127756365
Call-ID: 188ff6f804f343a685fbcb5ae52cead2
CSeq: 1 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:35:34 479] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1925401786
From: <sip:34020000001320000001@3402000000>;tag=1695713586
To: <sip:34020000002000000001@3402000000>
Call-ID: 971638101
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   323

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>DeviceInfo</CmdType>
<SN>10000</SN>
<DeviceID>34020000001320000001</DeviceID>
<Result>OK</Result>
<DeviceName>IP CAMERA</DeviceName>
<Manufacturer>Hikvision</Manufacturer>
<Model>DS-2CD1T45-LA</Model>
<Firmware>V5.8.0</Firmware>
<Channel>1</Channel>
</Response>
时间[10:35:34 480] 解析: 返回设备信息


时间[10:35:34 481] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1925401786
From: <sip:34020000001320000001@3402000000>;tag=1695713586
To: <sip:34020000002000000001@3402000000>
Call-ID: 971638101
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:34.477
Content-Length: 0


时间[10:35:34 482] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK60d4865a03084fb4b849307c9a0edd73
From: <sip:34020000002000000001@**************:15060>;tag=1001
To: <sip:34020000001320000001@*************:52309>
Call-ID: d735b79742774373937833a4e22d47d5
CSeq: 2 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10001</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:35:34 482] 解析: 获取设备通道


时间[10:35:34 483] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK7aa8048c336e4c518fa22899eb4c5848
From: <sip:34020000002000000001@**************:15060>;tag=1002
To: <sip:34020000001320000001@*************:52309>
Call-ID: ebbffdaca4f54c5a92c9aabed1adb2bd
CSeq: 3 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 197
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>ConfigDownload</CmdType>
  <SN>10002</SN>
  <DeviceID>34020000001320000001</DeviceID>
  <ConfigType>BasicParam</ConfigType>
</Query>
时间[10:35:34 484] 解析: 获取设备配置


时间[10:35:34 671] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK3bb8d6106a3f41f898362cae524854c0
From: <sip:34020000002000000001@**************:15060>;tag=1003
To: <sip:34020000001320000001@*************:52309>
Call-ID: 89c2eabc55e74fbfb0dfbdd040087e2b
CSeq: 4 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10003</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:35:34 673] 解析: 获取设备通道


时间[10:35:34 806] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK60d4865a03084fb4b849307c9a0edd73
From: <sip:34020000002000000001@**************:15060>;tag=1001
To: <sip:34020000001320000001@*************:52309>;tag=1457549013
Call-ID: d735b79742774373937833a4e22d47d5
CSeq: 2 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:35:34 808] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK7aa8048c336e4c518fa22899eb4c5848
From: <sip:34020000002000000001@**************:15060>;tag=1002
To: <sip:34020000001320000001@*************:52309>;tag=1613092392
Call-ID: ebbffdaca4f54c5a92c9aabed1adb2bd
CSeq: 3 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:35:34 809] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1713583200
From: <sip:34020000001320000001@3402000000>;tag=70668682
To: <sip:34020000002000000001@3402000000>
Call-ID: 946272962
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10001</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:35:34 810] 解析: 返回设备通道


时间[10:35:34 811] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1713583200
From: <sip:34020000001320000001@3402000000>;tag=70668682
To: <sip:34020000002000000001@3402000000>
Call-ID: 946272962
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:34.807
Content-Length: 0


时间[10:35:34 811] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK508543698
From: <sip:34020000001320000001@3402000000>;tag=285551450
To: <sip:34020000002000000001@3402000000>
Call-ID: 402195070
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   375

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>ConfigDownload</CmdType>
<SN>10002</SN>
<DeviceID>34020000001320000001</DeviceID>
<Result>OK</Result>
<BasicParam>
<Name>IP CAMERA</Name>
<Expiration>3600</Expiration>
<HeartBeatInterval>10</HeartBeatInterval>
<HeartBeatCount>3</HeartBeatCount>
<PositionCapability>0</PositionCapability>
</BasicParam>
</Response>
时间[10:35:34 812] 解析: 返回设备配置


时间[10:35:34 813] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK508543698
From: <sip:34020000001320000001@3402000000>;tag=285551450
To: <sip:34020000002000000001@3402000000>
Call-ID: 402195070
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:34.807
Content-Length: 0


时间[10:35:34 813] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK3bb8d6106a3f41f898362cae524854c0
From: <sip:34020000002000000001@**************:15060>;tag=1003
To: <sip:34020000001320000001@*************:52309>;tag=551333740
Call-ID: 89c2eabc55e74fbfb0dfbdd040087e2b
CSeq: 4 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:35:34 813] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1567262560
From: <sip:34020000001320000001@3402000000>;tag=1748961505
To: <sip:34020000002000000001@3402000000>
Call-ID: 1377139598
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10003</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:35:34 814] 解析: 返回设备通道


时间[10:35:34 815] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1567262560
From: <sip:34020000001320000001@3402000000>;tag=1748961505
To: <sip:34020000002000000001@3402000000>
Call-ID: 1377139598
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:34.807
Content-Length: 0


时间[10:35:39 192] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK732861505
From: <sip:34020000001320000001@3402000000>;tag=1992147571
To: <sip:34020000002000000001@3402000000>
Call-ID: 160052772
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>670</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:35:39 194] 解析: 返回设备心跳


时间[10:35:39 195] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK732861505
From: <sip:34020000001320000001@3402000000>;tag=1992147571
To: <sip:34020000002000000001@3402000000>
Call-ID: 160052772
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:39.192
Content-Length: 0


时间[10:35:46 671] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKcaf4dd5ab62849298083a76e452f870e
From: <sip:34020000002000000001@**************:15060>;tag=1004
To: <sip:34020000001320000001@*************:52309>
Call-ID: 12239e9f38474bcb83a0ac95750fbb3f
CSeq: 5 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10004</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:35:46 672] 解析: 获取设备通道


时间[10:35:46 843] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKcaf4dd5ab62849298083a76e452f870e
From: <sip:34020000002000000001@**************:15060>;tag=1004
To: <sip:34020000001320000001@*************:52309>;tag=1261551644
Call-ID: 12239e9f38474bcb83a0ac95750fbb3f
CSeq: 5 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:35:46 844] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK376938861
From: <sip:34020000001320000001@3402000000>;tag=1731854245
To: <sip:34020000002000000001@3402000000>
Call-ID: 754882454
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10004</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:35:46 847] 解析: 返回设备通道


时间[10:35:46 848] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK376938861
From: <sip:34020000001320000001@3402000000>;tag=1731854245
To: <sip:34020000002000000001@3402000000>
Call-ID: 754882454
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:46.842
Content-Length: 0


时间[10:35:49 149] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK193902612
From: <sip:34020000001320000001@3402000000>;tag=1550845780
To: <sip:34020000002000000001@3402000000>
Call-ID: 695847109
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>671</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:35:49 151] 解析: 返回设备心跳


时间[10:35:49 152] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK193902612
From: <sip:34020000001320000001@3402000000>;tag=1550845780
To: <sip:34020000002000000001@3402000000>
Call-ID: 695847109
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:49.149
Content-Length: 0


时间[10:35:58 670] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK4fc5ef8e2c1644ba80a54a8b94438682
From: <sip:34020000002000000001@**************:15060>;tag=1005
To: <sip:34020000001320000001@*************:52309>
Call-ID: 1465232d9c0a4d2188cb801d4836599d
CSeq: 6 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10005</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:35:58 672] 解析: 获取设备通道


时间[10:35:58 901] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK4fc5ef8e2c1644ba80a54a8b94438682
From: <sip:34020000002000000001@**************:15060>;tag=1005
To: <sip:34020000001320000001@*************:52309>;tag=832948483
Call-ID: 1465232d9c0a4d2188cb801d4836599d
CSeq: 6 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:35:58 902] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK693294180
From: <sip:34020000001320000001@3402000000>;tag=645473399
To: <sip:34020000002000000001@3402000000>
Call-ID: 1023076019
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10005</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:35:58 903] 解析: 返回设备通道


时间[10:35:58 904] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK693294180
From: <sip:34020000001320000001@3402000000>;tag=645473399
To: <sip:34020000002000000001@3402000000>
Call-ID: 1023076019
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:58.889
Content-Length: 0


时间[10:35:59 114] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK76127136
From: <sip:34020000001320000001@3402000000>;tag=1831254778
To: <sip:34020000002000000001@3402000000>
Call-ID: 1940836222
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>672</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:35:59 115] 解析: 返回设备心跳


时间[10:35:59 116] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK76127136
From: <sip:34020000001320000001@3402000000>;tag=1831254778
To: <sip:34020000002000000001@3402000000>
Call-ID: 1940836222
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:35:59.114
Content-Length: 0


时间[10:36:09 163] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1616568857
From: <sip:34020000001320000001@3402000000>;tag=984415676
To: <sip:34020000002000000001@3402000000>
Call-ID: 1990642886
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>673</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:36:09 164] 解析: 返回设备心跳


时间[10:36:09 164] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1616568857
From: <sip:34020000001320000001@3402000000>;tag=984415676
To: <sip:34020000002000000001@3402000000>
Call-ID: 1990642886
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:09.163
Content-Length: 0


时间[10:36:10 671] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKc3cccf67f12c423a91ee72ca6ea8ba2e
From: <sip:34020000002000000001@**************:15060>;tag=1006
To: <sip:34020000001320000001@*************:52309>
Call-ID: 1ccf1fae18e64b36a997c81259e9b52d
CSeq: 7 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10006</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:36:10 674] 解析: 获取设备通道


时间[10:36:10 809] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKc3cccf67f12c423a91ee72ca6ea8ba2e
From: <sip:34020000002000000001@**************:15060>;tag=1006
To: <sip:34020000001320000001@*************:52309>;tag=254694469
Call-ID: 1ccf1fae18e64b36a997c81259e9b52d
CSeq: 7 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:36:10 810] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK375691233
From: <sip:34020000001320000001@3402000000>;tag=459144063
To: <sip:34020000002000000001@3402000000>
Call-ID: 2018691844
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10006</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:36:10 811] 解析: 返回设备通道


时间[10:36:10 811] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK375691233
From: <sip:34020000001320000001@3402000000>;tag=459144063
To: <sip:34020000002000000001@3402000000>
Call-ID: 2018691844
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:10.808
Content-Length: 0


时间[10:36:19 138] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1990226472
From: <sip:34020000001320000001@3402000000>;tag=952137126
To: <sip:34020000002000000001@3402000000>
Call-ID: 1358565774
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>674</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:36:19 140] 解析: 返回设备心跳


时间[10:36:19 141] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1990226472
From: <sip:34020000001320000001@3402000000>;tag=952137126
To: <sip:34020000002000000001@3402000000>
Call-ID: 1358565774
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:19.137
Content-Length: 0


时间[10:36:22 673] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKce5d7f873b2f47d18fbaa9499a57d693
From: <sip:34020000002000000001@**************:15060>;tag=1007
To: <sip:34020000001320000001@*************:52309>
Call-ID: 3c70c2bf71a34f4f9610fc83cba9344e
CSeq: 8 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10007</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:36:22 674] 解析: 获取设备通道


时间[10:36:22 872] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKce5d7f873b2f47d18fbaa9499a57d693
From: <sip:34020000002000000001@**************:15060>;tag=1007
To: <sip:34020000001320000001@*************:52309>;tag=1048674986
Call-ID: 3c70c2bf71a34f4f9610fc83cba9344e
CSeq: 8 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:36:22 874] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK129848430
From: <sip:34020000001320000001@3402000000>;tag=1341009825
To: <sip:34020000002000000001@3402000000>
Call-ID: 2012685058
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10007</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:36:22 875] 解析: 返回设备通道


时间[10:36:22 875] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK129848430
From: <sip:34020000001320000001@3402000000>;tag=1341009825
To: <sip:34020000002000000001@3402000000>
Call-ID: 2012685058
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:22.871
Content-Length: 0


时间[10:36:29 103] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK10932208
From: <sip:34020000001320000001@3402000000>;tag=763869333
To: <sip:34020000002000000001@3402000000>
Call-ID: 1930649951
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>675</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:36:29 105] 解析: 返回设备心跳


时间[10:36:29 105] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK10932208
From: <sip:34020000001320000001@3402000000>;tag=763869333
To: <sip:34020000002000000001@3402000000>
Call-ID: 1930649951
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:29.103
Content-Length: 0


时间[10:36:34 672] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK0f99e1166baa4409bbf187cd6ee8a2dc
From: <sip:34020000002000000001@**************:15060>;tag=1008
To: <sip:34020000001320000001@*************:52309>
Call-ID: 7d197486297f4b2a882cbe1e29b1c94f
CSeq: 9 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10008</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:36:34 673] 解析: 获取设备通道


时间[10:36:34 795] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK0f99e1166baa4409bbf187cd6ee8a2dc
From: <sip:34020000002000000001@**************:15060>;tag=1008
To: <sip:34020000001320000001@*************:52309>;tag=1727911849
Call-ID: 7d197486297f4b2a882cbe1e29b1c94f
CSeq: 9 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:36:34 795] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK775693681
From: <sip:34020000001320000001@3402000000>;tag=1917198717
To: <sip:34020000002000000001@3402000000>
Call-ID: 1965288308
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10008</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:36:34 796] 解析: 返回设备通道


时间[10:36:34 796] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK775693681
From: <sip:34020000001320000001@3402000000>;tag=1917198717
To: <sip:34020000002000000001@3402000000>
Call-ID: 1965288308
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:34.795
Content-Length: 0


时间[10:36:39 165] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK296699240
From: <sip:34020000001320000001@3402000000>;tag=1042566333
To: <sip:34020000002000000001@3402000000>
Call-ID: 1624317478
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>676</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:36:39 168] 解析: 返回设备心跳


时间[10:36:39 168] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK296699240
From: <sip:34020000001320000001@3402000000>;tag=1042566333
To: <sip:34020000002000000001@3402000000>
Call-ID: 1624317478
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:39.164
Content-Length: 0


时间[10:36:46 671] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKbf24ae55338e4206a18f9f0a5106c910
From: <sip:34020000002000000001@**************:15060>;tag=1009
To: <sip:34020000001320000001@*************:52309>
Call-ID: 37e62e1eae2f49a68332b68b4ef6cc78
CSeq: 10 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10009</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:36:46 672] 解析: 获取设备通道


时间[10:36:46 846] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKbf24ae55338e4206a18f9f0a5106c910
From: <sip:34020000002000000001@**************:15060>;tag=1009
To: <sip:34020000001320000001@*************:52309>;tag=461265570
Call-ID: 37e62e1eae2f49a68332b68b4ef6cc78
CSeq: 10 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:36:46 849] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK987125870
From: <sip:34020000001320000001@3402000000>;tag=1249044703
To: <sip:34020000002000000001@3402000000>
Call-ID: 2076433121
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10009</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:36:46 852] 解析: 返回设备通道


时间[10:36:46 852] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK987125870
From: <sip:34020000001320000001@3402000000>;tag=1249044703
To: <sip:34020000002000000001@3402000000>
Call-ID: 2076433121
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:46.847
Content-Length: 0


时间[10:36:49 135] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1474120265
From: <sip:34020000001320000001@3402000000>;tag=340593205
To: <sip:34020000002000000001@3402000000>
Call-ID: 1792038071
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>677</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:36:49 136] 解析: 返回设备心跳


时间[10:36:49 137] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1474120265
From: <sip:34020000001320000001@3402000000>;tag=340593205
To: <sip:34020000002000000001@3402000000>
Call-ID: 1792038071
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:49.135
Content-Length: 0


时间[10:36:58 666] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK28c3ac925c324916a44c8b7e53f314ce
From: <sip:34020000002000000001@**************:15060>;tag=1010
To: <sip:34020000001320000001@*************:52309>
Call-ID: f6f0bf69d5c247efbadbb3accbb830fd
CSeq: 11 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10010</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:36:58 668] 解析: 获取设备通道


时间[10:36:58 868] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK28c3ac925c324916a44c8b7e53f314ce
From: <sip:34020000002000000001@**************:15060>;tag=1010
To: <sip:34020000001320000001@*************:52309>;tag=154126297
Call-ID: f6f0bf69d5c247efbadbb3accbb830fd
CSeq: 11 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:36:58 871] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK56388888
From: <sip:34020000001320000001@3402000000>;tag=44692494
To: <sip:34020000002000000001@3402000000>
Call-ID: 1659095793
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10010</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:36:58 875] 解析: 返回设备通道


时间[10:36:58 875] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK56388888
From: <sip:34020000001320000001@3402000000>;tag=44692494
To: <sip:34020000002000000001@3402000000>
Call-ID: 1659095793
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:58.867
Content-Length: 0


时间[10:36:59 208] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK744888117
From: <sip:34020000001320000001@3402000000>;tag=199612022
To: <sip:34020000002000000001@3402000000>
Call-ID: 1810020059
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>678</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:36:59 211] 解析: 返回设备心跳


时间[10:36:59 211] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK744888117
From: <sip:34020000001320000001@3402000000>;tag=199612022
To: <sip:34020000002000000001@3402000000>
Call-ID: 1810020059
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:36:59.208
Content-Length: 0


时间[10:37:07 753] 发送: INVITE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK830e807a049c43d982290630b031cb31
From: <sip:34020000002000000001@**************:15060>;tag=1011
To: <sip:34020000001320000001@*************:52309>
Call-ID: 51c2b56fcc754c4288823b1512a19342
CSeq: 12 INVITE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 322
Content-Type: application/sdp
Contact: <sip:34020000002000000001@**************:15060>
Subject: 34020000001320000001:0000010000,34020000002000000001:1

v=0
o=34020000001320000001 0 0 IN IP4 **************
s=Play
u=34020000001320000001:0
c=IN IP4 **************
t=0 0
m=video 6900 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=0000010000

时间[10:37:07 755] 解析: 打开通道视频


时间[10:37:07 954] 接收: SIP/2.0 100 Trying
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK830e807a049c43d982290630b031cb31
From: <sip:34020000002000000001@**************:15060>;tag=1011
To: <sip:34020000001320000001@*************:52309>
Call-ID: 51c2b56fcc754c4288823b1512a19342
CSeq: 12 INVITE
User-Agent: IP Camera
Content-Length: 0


时间[10:37:07 956] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK830e807a049c43d982290630b031cb31
From: <sip:34020000002000000001@**************:15060>;tag=1011
To: <sip:34020000001320000001@*************:52309>;tag=1792400247
Call-ID: 51c2b56fcc754c4288823b1512a19342
CSeq: 12 INVITE
Contact: <sip:34020000001320000001@*************:52309>
Content-Type: application/sdp
User-Agent: IP Camera
Content-Length:   231

v=0
o=34020000001320000001 234 234 IN IP4 *************
s=Play
c=IN IP4 *************
t=0 0
m=video 15060 RTP/AVP 96
a=sendonly
a=streamnumber:0
a=rtpmap:96 PS/90000
a=filesize:0
y=0000010000
f=v/2/6/25/1/6144a/6/6/3
时间[10:37:07 957] 发送: ACK sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK830e807a049c43d982290630b031cb31
From: <sip:34020000002000000001@**************:15060>;tag=1011
To: <sip:34020000001320000001@*************:52309>;tag=1792400247
Call-ID: 51c2b56fcc754c4288823b1512a19342
CSeq: 12 ACK


时间[10:37:07 957] 解析: 开始播放视频


时间[10:37:07 958] 提示: 视频传输模式: Udp - 15060


时间[10:37:09 152] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK419080451
From: <sip:34020000001320000001@3402000000>;tag=1371571460
To: <sip:34020000002000000001@3402000000>
Call-ID: 2140520745
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>679</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:37:09 154] 解析: 返回设备心跳


时间[10:37:09 155] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK419080451
From: <sip:34020000001320000001@3402000000>;tag=1371571460
To: <sip:34020000002000000001@3402000000>
Call-ID: 2140520745
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:37:09.151
Content-Length: 0


时间[10:37:10 504] 发送: INVITE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK71bc142d585d42c199bddf0b63619a03
From: <sip:34020000002000000001@**************:15060>;tag=1012
To: <sip:34020000001320000001@*************:52309>
Call-ID: 0a144c5e02f44fa2a8882656bc971095
CSeq: 13 INVITE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 322
Content-Type: application/sdp
Contact: <sip:34020000002000000001@**************:15060>
Subject: 34020000001320000001:0000010001,34020000002000000001:1

v=0
o=34020000001320000001 0 0 IN IP4 **************
s=Play
u=34020000001320000001:0
c=IN IP4 **************
t=0 0
m=video 6902 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=0000010001

时间[10:37:10 507] 解析: 打开通道视频


时间[10:37:10 673] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK8f64dde1629040f4b921fa3ef05c7f4e
From: <sip:34020000002000000001@**************:15060>;tag=1013
To: <sip:34020000001320000001@*************:52309>
Call-ID: b28407af56fa4ed6b82427fb9209973d
CSeq: 14 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10011</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:37:10 674] 解析: 获取设备通道


时间[10:37:10 797] 接收: SIP/2.0 100 Trying
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK71bc142d585d42c199bddf0b63619a03
From: <sip:34020000002000000001@**************:15060>;tag=1012
To: <sip:34020000001320000001@*************:52309>
Call-ID: 0a144c5e02f44fa2a8882656bc971095
CSeq: 13 INVITE
User-Agent: IP Camera
Content-Length: 0


时间[10:37:10 799] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK71bc142d585d42c199bddf0b63619a03
From: <sip:34020000002000000001@**************:15060>;tag=1012
To: <sip:34020000001320000001@*************:52309>;tag=38132670
Call-ID: 0a144c5e02f44fa2a8882656bc971095
CSeq: 13 INVITE
Contact: <sip:34020000001320000001@*************:52309>
Content-Type: application/sdp
User-Agent: IP Camera
Content-Length:   231

v=0
o=34020000001320000001 921 921 IN IP4 *************
s=Play
c=IN IP4 *************
t=0 0
m=video 15064 RTP/AVP 96
a=sendonly
a=streamnumber:0
a=rtpmap:96 PS/90000
a=filesize:0
y=0000010001
f=v/2/6/25/1/6144a/6/6/3
时间[10:37:10 802] 发送: ACK sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK71bc142d585d42c199bddf0b63619a03
From: <sip:34020000002000000001@**************:15060>;tag=1012
To: <sip:34020000001320000001@*************:52309>;tag=38132670
Call-ID: 0a144c5e02f44fa2a8882656bc971095
CSeq: 13 ACK


时间[10:37:10 803] 解析: 开始播放视频


时间[10:37:10 804] 提示: 视频传输模式: Udp - 15064


时间[10:37:10 806] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK8f64dde1629040f4b921fa3ef05c7f4e
From: <sip:34020000002000000001@**************:15060>;tag=1013
To: <sip:34020000001320000001@*************:52309>;tag=1067464575
Call-ID: b28407af56fa4ed6b82427fb9209973d
CSeq: 14 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:37:10 806] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK101259218
From: <sip:34020000001320000001@3402000000>;tag=1985312202
To: <sip:34020000002000000001@3402000000>
Call-ID: 1507343372
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10011</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:37:10 807] 解析: 返回设备通道


时间[10:37:10 807] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK101259218
From: <sip:34020000001320000001@3402000000>;tag=1985312202
To: <sip:34020000002000000001@3402000000>
Call-ID: 1507343372
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:37:10.797
Content-Length: 0


时间[10:37:19 214] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK169192227
From: <sip:34020000001320000001@3402000000>;tag=2077821078
To: <sip:34020000002000000001@3402000000>
Call-ID: 2071959939
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>680</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:37:19 217] 解析: 返回设备心跳


时间[10:37:19 222] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK169192227
From: <sip:34020000001320000001@3402000000>;tag=2077821078
To: <sip:34020000002000000001@3402000000>
Call-ID: 2071959939
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:37:19.213
Content-Length: 0


时间[10:37:22 666] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK67cbaee68a934c23bd5d91a4f567da71
From: <sip:34020000002000000001@**************:15060>;tag=1014
To: <sip:34020000001320000001@*************:52309>
Call-ID: 62ce31323e6a4042b0bfc91a6278ff29
CSeq: 15 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10012</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:37:22 668] 解析: 获取设备通道


时间[10:37:22 819] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK67cbaee68a934c23bd5d91a4f567da71
From: <sip:34020000002000000001@**************:15060>;tag=1014
To: <sip:34020000001320000001@*************:52309>;tag=1403181216
Call-ID: 62ce31323e6a4042b0bfc91a6278ff29
CSeq: 15 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:37:22 821] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1740559890
From: <sip:34020000001320000001@3402000000>;tag=286062985
To: <sip:34020000002000000001@3402000000>
Call-ID: 1781774160
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10012</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:37:22 822] 解析: 返回设备通道


时间[10:37:22 823] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK1740559890
From: <sip:34020000001320000001@3402000000>;tag=286062985
To: <sip:34020000002000000001@3402000000>
Call-ID: 1781774160
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:37:22.819
Content-Length: 0


时间[10:37:29 174] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK404086620
From: <sip:34020000001320000001@3402000000>;tag=30528798
To: <sip:34020000002000000001@3402000000>
Call-ID: 1502982500
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>681</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:37:29 176] 解析: 返回设备心跳


时间[10:37:29 177] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK404086620
From: <sip:34020000001320000001@3402000000>;tag=30528798
To: <sip:34020000002000000001@3402000000>
Call-ID: 1502982500
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:37:29.174
Content-Length: 0


时间[10:37:34 671] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK09bf4e6054174fbebf72ef002ea9bbf7
From: <sip:34020000002000000001@**************:15060>;tag=1015
To: <sip:34020000001320000001@*************:52309>
Call-ID: 5cb0d00ed9b54fe782e2a571a811e0ff
CSeq: 16 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10013</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:37:34 672] 解析: 获取设备通道


时间[10:37:34 855] 接收: SIP/2.0 200 OK
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bK09bf4e6054174fbebf72ef002ea9bbf7
From: <sip:34020000002000000001@**************:15060>;tag=1015
To: <sip:34020000001320000001@*************:52309>;tag=786535788
Call-ID: 5cb0d00ed9b54fe782e2a571a811e0ff
CSeq: 16 MESSAGE
User-Agent: IP Camera
Content-Length: 0


时间[10:37:34 857] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK2140116582
From: <sip:34020000001320000001@3402000000>;tag=688602607
To: <sip:34020000002000000001@3402000000>
Call-ID: 34017612
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   643

<?xml version="1.0" encoding="GB18030"?>
<Response>
<CmdType>Catalog</CmdType>
<SN>10013</SN>
<DeviceID>34020000001320000001</DeviceID>
<SumNum>1</SumNum>
<DeviceList Num="1">
<Item>
<DeviceID>34020000001320000001</DeviceID>
<Name>Camera 01</Name>
<Manufacturer>Hikvision</Manufacturer>
<Model>IP Camera</Model>
<Owner>Owner</Owner>
<CivilCode>3402000000</CivilCode>
<Address>Address</Address>
<Parental>0</Parental>
<ParentID>34020000002000000001</ParentID>
<SafetyWay>0</SafetyWay>
<RegisterWay>1</RegisterWay>
<Secrecy>0</Secrecy>
<Status>ON</Status>
<IPAddress>*************</IPAddress>
<Port>5060</Port>
</Item>
</DeviceList>
</Response>
时间[10:37:34 859] 解析: 返回设备通道


时间[10:37:34 859] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK2140116582
From: <sip:34020000001320000001@3402000000>;tag=688602607
To: <sip:34020000002000000001@3402000000>
Call-ID: 34017612
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:37:34.854
Content-Length: 0


时间[10:37:39 118] 接收: MESSAGE sip:34020000002000000001@3402000000 SIP/2.0
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK596442188
From: <sip:34020000001320000001@3402000000>;tag=15482969
To: <sip:34020000002000000001@3402000000>
Call-ID: 1153694108
CSeq: 20 MESSAGE
Content-Type: Application/MANSCDP+xml
Max-Forwards: 70
User-Agent: IP Camera
Content-Length:   179

<?xml version="1.0" encoding="GB18030"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>682</SN>
<DeviceID>34020000001320000001</DeviceID>
<Status>OK</Status>
<Info>
</Info>
</Notify>

时间[10:37:39 121] 解析: 返回设备心跳


时间[10:37:39 122] 发送: SIP/2.0 200 OK
Via: SIP/2.0/TCP *************:52309;rport;branch=z9hG4bK596442188
From: <sip:34020000001320000001@3402000000>;tag=15482969
To: <sip:34020000002000000001@3402000000>
Call-ID: 1153694108
CSeq: 20 MESSAGE
User-Agent: wx_feiyangqingyun
Date: 2025-08-28T10:37:39.117
Content-Length: 0


时间[10:37:46 711] 发送: MESSAGE sip:34020000001320000001@*************:52309 SIP/2.0
Via: SIP/2.0/TCP **************:15060;branch=z9hG4bKb00da82b414744d890ead5d059a4837e
From: <sip:34020000002000000001@**************:15060>;tag=1016
To: <sip:34020000001320000001@*************:52309>
Call-ID: 89884f07b0534bf0a2bd3289725c1b7d
CSeq: 17 MESSAGE
User-Agent: wx_feiyangqingyun
Max-Forwards: 70
Content-Length: 151
Content-Type: application/MANSCDP+xml
Contact: <sip:34020000002000000001@**************:15060>

<?xml version="1.0" encoding="GB2312"?>
<Query>
  <CmdType>Catalog</CmdType>
  <SN>10014</SN>
  <DeviceID>34020000001320000001</DeviceID>
</Query>
时间[10:37:46 743] 解析: 获取设备通道


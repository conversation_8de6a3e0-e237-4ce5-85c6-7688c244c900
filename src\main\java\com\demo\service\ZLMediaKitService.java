package com.demo.service;

import com.demo.config.GB28181Properties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ZLMediaKit集成服务
 * 负责与ZLMediaKit流媒体服务器的交互
 */
@Slf4j
@Service
public class ZLMediaKitService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private GB28181Properties properties;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * RTP端口计数器，用于分配端口
     */
    private final AtomicInteger portCounter = new AtomicInteger();
    
    /**
     * 初始化端口计数器
     */
    @PostConstruct
    public void initPortCounter() {
        portCounter.set(properties.getZlm().getRtpPortStart());
    }
    
    /**
     * 分配RTP端口
     * 
     * @return 分配的端口号
     */
    public int allocateRtpPort() {
        int port = portCounter.getAndIncrement();
        
        // 如果超过端口范围，重新从起始端口开始
        if (port > properties.getZlm().getRtpPortEnd()) {
            portCounter.set(properties.getZlm().getRtpPortStart());
            port = portCounter.getAndIncrement();
        }
        
        return port;
    }
    
    /**
     * 通知ZLMediaKit准备接收RTP流
     * 
     * @param deviceId 设备ID
     * @param rtpPort RTP端口
     * @return 是否成功
     */
    public boolean prepareRtpReceive(String deviceId, int rtpPort) {
        try {
            String url = properties.getZlm().getApiBaseUrl(properties.getNetwork()) + "/index/api/openRtpServer";

            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            // 构建表单参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("secret", properties.getZlm().getSecret());
            params.add("port", String.valueOf(rtpPort));
            params.add("stream_id", deviceId);
            params.add("tcp_mode", "1"); // 0=UDP, 1=TCP
            params.add("timeout_sec", "30"); // 增加超时时间到30秒
            params.add("re_use_port", "1"); // 允许端口复用

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                int code = jsonNode.get("code").asInt();
                
                if (code == 0) {
                    log.info("ZLMediaKit准备接收RTP成功: 设备={}, 端口={}", deviceId, rtpPort);
                    return true;
                } else {
                    String msg = jsonNode.has("msg") ? jsonNode.get("msg").asText() : "";
                    // 已存在流/服务器的情况视为可复用成功
                    if (msg != null && msg.toLowerCase().contains("already exists")) {
                        log.info("ZLMediaKit已存在RTP接收: 设备={}, 端口={}，复用成功", deviceId, rtpPort);
                        return true;
                    }
                    log.error("ZLMediaKit准备接收RTP失败: 设备={}, 端口={}, 错误={}", deviceId, rtpPort, msg);
                }
            }
            
        } catch (Exception e) {
            log.error("通知ZLMediaKit准备接收RTP失败: 设备={}, 端口={}", deviceId, rtpPort, e);
        }
        
        return false;
    }
    
    /**
     * 关闭RTP服务器
     * 
     * @param deviceId 设备ID
     * @param rtpPort RTP端口
     * @return 是否成功
     */
    public boolean closeRtpServer(String deviceId, int rtpPort) {
        try {
            String url = properties.getZlm().getApiBaseUrl(properties.getNetwork()) + "/index/api/closeRtpServer";

            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            // 构建表单参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("secret", properties.getZlm().getSecret());
            params.add("stream_id", deviceId);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                int code = jsonNode.get("code").asInt();
                
                if (code == 0) {
                    log.info("关闭RTP服务器成功: 设备={}, 端口={}", deviceId, rtpPort);
                    return true;
                } else {
                    log.info("关闭RTP服务器失败: 设备={}, 端口={}, 错误={}", 
                            deviceId, rtpPort, jsonNode.get("msg").asText());
                }
            }
            
        } catch (Exception e) {
            log.error("关闭RTP服务器失败: 设备={}, 端口={}", deviceId, rtpPort, e);
        }
        
        return false;
    }
    
    /**
     * 获取流媒体列表
     * 
     * @return 流媒体信息
     */
    public String getMediaList() {
        try {
            String url = properties.getZlm().getApiBaseUrl(properties.getNetwork()) + "/index/api/getMediaList";

            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            // 构建表单参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("secret", properties.getZlm().getSecret());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            }
            
        } catch (Exception e) {
            log.error("获取流媒体列表失败", e);
        }
        
        return null;
    }
    
    /**
     * 检查流是否存在
     * 
     * @param deviceId 设备ID（流ID）
     * @return 是否存在
     */
    public boolean isStreamExists(String deviceId) {
        try {
            String mediaList = getMediaList();
            if (mediaList != null) {
                JsonNode jsonNode = objectMapper.readTree(mediaList);
                JsonNode dataNode = jsonNode.get("data");
                
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode streamNode : dataNode) {
                        String streamId = streamNode.get("stream").asText();
                        if (deviceId.equals(streamId)) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查流是否存在失败: {}", deviceId, e);
        }
        
        return false;
    }
    
    /**
     * 生成播放URL
     *
     * @param deviceId 设备ID
     * @param format 格式 (flv, hls, rtmp等)
     * @return 播放URL
     */
    public String generatePlayUrl(String deviceId, String format) {
        String baseUrl = properties.getZlm().getApiBaseUrl(properties.getNetwork());
        String zlmHost = properties.getNetwork().getZlmIp();

        switch (format.toLowerCase()) {
            case "flv":
                return String.format("%s/rtp/%s.flv", baseUrl, deviceId);
            case "hls":
            case "m3u8":
                return String.format("%s/rtp/%s/hls.m3u8", baseUrl, deviceId);
            case "rtsp":
                return String.format("rtsp://%s:554/rtp/%s", zlmHost, deviceId);
            case "rtmp":
                return String.format("rtmp://%s:1935/rtp/%s", zlmHost, deviceId);
            default:
                return String.format("%s/rtp/%s.flv", baseUrl, deviceId);
        }
    }

    /**
     * 检查RTP服务器是否活跃
     *
     * @param deviceId 设备ID
     * @return 是否活跃
     */
    public boolean isRtpServerActive(String deviceId) {
        // 简化实现：通过检查流是否存在来判断RTP服务器状态
        return isStreamExists(deviceId);
    }

}

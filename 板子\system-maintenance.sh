#!/bin/bash
# 系统维护工具 - 集成多摄像头管理和音频故障排除
# 交互式菜单操作

set -e

VIRTUAL_SOURCE="countryside_audio_source"
BASE_DIR="/home/<USER>/srs"
CONFIG_FILE="$BASE_DIR/camera_config.json"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 状态打印函数
print_status() {
    case $1 in
        "OK") echo -e "${GREEN}✅ $2${NC}" ;;
        "WARN") echo -e "${YELLOW}⚠️  $2${NC}" ;;
        "ERROR") echo -e "${RED}❌ $2${NC}" ;;
        "INFO") echo -e "${BLUE}ℹ️  $2${NC}" ;;
        "TITLE") echo -e "${CYAN}🔧 $2${NC}" ;;
    esac
}

# 显示主菜单
show_main_menu() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}    农村两轮车系统维护工具    ${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
    echo "请选择功能："
    echo ""
    echo "📹 摄像头管理："
    echo "  1. 查看摄像头状态"
    echo "  2. 添加摄像头"
    echo "  3. 删除摄像头"
    echo "  4. 重启推流服务"
    echo ""
    echo "🎤 音频诊断："
    echo "  5. 快速音频诊断"
    echo "  6. 测试音频功能"
    echo "  7. 检查设备冲突"
    echo "  8. 修复音频问题"
    echo ""
    echo "🔧 系统维护："
    echo "  9. 查看系统状态"
    echo "  10. 查看服务日志"
    echo "  11. 重启所有服务"
    echo ""
    echo "  0. 退出"
    echo ""
    echo -e "${CYAN}================================${NC}"
}

# 等待用户按键
wait_for_key() {
    echo ""
    read -p "按回车键继续..." -r
}

# 查看摄像头状态
show_camera_status() {
    print_status "TITLE" "摄像头状态"
    echo ""
    
    if [ ! -f "$CONFIG_FILE" ]; then
        print_status "ERROR" "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    if command -v jq >/dev/null 2>&1; then
        local camera_count=$(jq '.cameras | length' "$CONFIG_FILE" 2>/dev/null || echo "0")
        print_status "INFO" "配置的摄像头数量: $camera_count"
        echo ""
        
        jq -r '.cameras[] | "📷 \(.name) (\(.ip)) - 流名称: \(.stream_name)"' "$CONFIG_FILE" 2>/dev/null || {
            print_status "ERROR" "解析配置文件失败"
            return 1
        }
    else
        print_status "WARN" "jq未安装，显示原始配置"
        cat "$CONFIG_FILE"
    fi
    
    echo ""
    # 检查推流服务状态
    if systemctl is-active --quiet multi-camera-push 2>/dev/null; then
        print_status "OK" "推流服务运行中"
    else
        print_status "ERROR" "推流服务未运行"
    fi
}

# 添加摄像头
add_camera() {
    print_status "TITLE" "添加摄像头"
    echo ""
    
    if [ ! -f "$CONFIG_FILE" ]; then
        print_status "ERROR" "配置文件不存在，请先运行 smart-stream-deploy.sh"
        return 1
    fi
    
    echo "请输入摄像头信息："
    read -p "摄像头名称: " cam_name
    read -p "摄像头IP: " cam_ip
    read -p "用户名 [admin]: " cam_user
    cam_user=${cam_user:-admin}
    read -p "密码 [ybda2025]: " cam_pass
    cam_pass=${cam_pass:-ybda2025}
    read -p "流名称 [stream-$cam_name]: " stream_name
    stream_name=${stream_name:-stream-$cam_name}
    
    local rtsp_url="rtsp://${cam_user}:${cam_pass}@${cam_ip}/LiveMedia/ch1/Media2"
    read -p "RTSP地址 [$rtsp_url]: " custom_rtsp
    rtsp_url=${custom_rtsp:-$rtsp_url}
    
    echo ""
    echo "确认添加摄像头："
    echo "  名称: $cam_name"
    echo "  IP: $cam_ip"
    echo "  流名称: $stream_name"
    echo "  RTSP: $rtsp_url"
    echo ""
    read -p "确认添加？(y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        # 这里应该添加到配置文件的逻辑
        print_status "INFO" "功能开发中，请手动编辑配置文件"
        print_status "INFO" "配置文件位置: $CONFIG_FILE"
    else
        print_status "INFO" "取消添加"
    fi
}

# 删除摄像头
remove_camera() {
    print_status "TITLE" "删除摄像头"
    echo ""
    
    if [ ! -f "$CONFIG_FILE" ]; then
        print_status "ERROR" "配置文件不存在"
        return 1
    fi
    
    if command -v jq >/dev/null 2>&1; then
        echo "当前摄像头列表："
        jq -r '.cameras[] | "📷 \(.name) (\(.ip))"' "$CONFIG_FILE" 2>/dev/null
        echo ""
        read -p "请输入要删除的摄像头名称: " cam_name
        
        if [ -n "$cam_name" ]; then
            print_status "INFO" "功能开发中，请手动编辑配置文件"
            print_status "INFO" "配置文件位置: $CONFIG_FILE"
        fi
    else
        print_status "ERROR" "需要安装jq: sudo apt install jq"
    fi
}

# 重启推流服务
restart_streaming() {
    print_status "TITLE" "重启推流服务"
    echo ""
    
    if systemctl is-active --quiet multi-camera-push 2>/dev/null; then
        print_status "INFO" "停止推流服务..."
        systemctl stop multi-camera-push
        sleep 2
    fi
    
    print_status "INFO" "启动推流服务..."
    systemctl start multi-camera-push
    sleep 3
    
    if systemctl is-active --quiet multi-camera-push 2>/dev/null; then
        print_status "OK" "推流服务重启成功"
    else
        print_status "ERROR" "推流服务启动失败"
        print_status "INFO" "查看日志: tail -n 20 $BASE_DIR/logs/multi-camera-push.log"
    fi
}

# 快速音频诊断
quick_audio_check() {
    print_status "TITLE" "快速音频诊断"
    echo ""
    
    # 检查PulseAudio
    if pgrep -x "pulseaudio" > /dev/null; then
        print_status "OK" "PulseAudio运行中"
    else
        print_status "ERROR" "PulseAudio未运行"
        echo "   修复命令: pulseaudio --start"
    fi
    
    # 检查虚拟音频源
    if pactl list sources short | grep -q "$VIRTUAL_SOURCE"; then
        print_status "OK" "虚拟音频源存在"
    else
        print_status "ERROR" "虚拟音频源缺失"
        echo "   修复方法: 重新运行 smart-stream-deploy.sh"
    fi
    
    # 检查配置文件
    if [ -f "$CONFIG_FILE" ]; then
        print_status "OK" "推流配置存在"
    else
        print_status "WARN" "推流配置缺失"
    fi
    
    # 检查WebRTC配置
    if [ -f "/home/<USER>/srs/auto_request.sh" ]; then
        print_status "OK" "WebRTC配置存在"
    else
        print_status "WARN" "WebRTC配置缺失"
    fi
}

# 测试音频功能
test_audio() {
    print_status "TITLE" "测试音频功能"
    echo ""
    
    if ! pactl list sources short | grep -q "$VIRTUAL_SOURCE"; then
        print_status "ERROR" "虚拟音频源不存在，跳过测试"
        return 1
    fi
    
    print_status "INFO" "测试音频录制（3秒）..."
    if timeout 3 parecord --device="$VIRTUAL_SOURCE" --file-format=wav /dev/null 2>/dev/null; then
        print_status "OK" "音频录制测试通过"
    else
        print_status "ERROR" "音频录制测试失败"
        return 1
    fi
    
    if command -v ffmpeg >/dev/null 2>&1; then
        print_status "INFO" "测试FFmpeg音频（3秒）..."
        if timeout 3 ffmpeg -f pulse -i "$VIRTUAL_SOURCE" -f null - 2>/dev/null; then
            print_status "OK" "FFmpeg音频测试通过"
        else
            print_status "ERROR" "FFmpeg音频测试失败"
        fi
    fi
}

# 检查设备冲突
check_conflicts() {
    print_status "TITLE" "检查设备冲突"
    echo ""
    
    local alsa_count=$(lsof /dev/snd/* 2>/dev/null | grep -v "PID" | wc -l)
    if [ "$alsa_count" -gt 2 ]; then
        print_status "WARN" "多个进程占用ALSA设备 ($alsa_count)"
        echo "占用进程:"
        lsof /dev/snd/* 2>/dev/null | grep -v "PID" | awk '{print "   " $1 " (PID: " $2 ")"}'
    else
        print_status "OK" "ALSA设备占用正常"
    fi
    
    local pulse_clients=$(pactl list clients short 2>/dev/null | wc -l)
    print_status "INFO" "PulseAudio客户端数量: $pulse_clients"
}

# 修复音频问题
fix_audio() {
    print_status "TITLE" "修复音频问题"
    echo ""
    
    echo "可用的修复选项："
    echo "1. 重启PulseAudio"
    echo "2. 重新创建虚拟音频源"
    echo "3. 重启推流服务"
    echo "4. 重启WebRTC服务"
    echo "0. 返回主菜单"
    echo ""
    read -p "请选择修复选项 (0-4): " fix_choice
    
    case $fix_choice in
        1)
            print_status "INFO" "重启PulseAudio..."
            pulseaudio --kill 2>/dev/null || true
            sleep 2
            pulseaudio --start --log-target=syslog 2>/dev/null || true
            print_status "OK" "PulseAudio已重启"
            ;;
        2)
            print_status "INFO" "请重新运行 smart-stream-deploy.sh 重新创建虚拟音频源"
            ;;
        3)
            restart_streaming
            ;;
        4)
            print_status "INFO" "重启WebRTC服务..."
            pkill chromium 2>/dev/null || true
            sleep 2
            if [ -f "/home/<USER>/srs/auto_request.sh" ]; then
                /home/<USER>/srs/auto_request.sh &
                print_status "OK" "WebRTC服务已重启"
            else
                print_status "ERROR" "WebRTC配置文件不存在"
            fi
            ;;
        0)
            return 0
            ;;
        *)
            print_status "ERROR" "无效选择"
            ;;
    esac
}

# 查看系统状态
show_system_status() {
    print_status "TITLE" "系统状态"
    echo ""
    
    # PulseAudio状态
    if pgrep -x "pulseaudio" > /dev/null; then
        print_status "OK" "PulseAudio: 运行中"
    else
        print_status "ERROR" "PulseAudio: 未运行"
    fi
    
    # 虚拟音频源
    if pactl list sources short | grep -q "$VIRTUAL_SOURCE"; then
        print_status "OK" "虚拟音频源: 存在"
    else
        print_status "ERROR" "虚拟音频源: 不存在"
    fi
    
    # 推流服务
    if systemctl is-active --quiet multi-camera-push 2>/dev/null; then
        print_status "OK" "推流服务: 运行中"
    else
        print_status "ERROR" "推流服务: 未运行"
    fi
    
    # WebRTC服务
    if pgrep chromium > /dev/null; then
        print_status "OK" "WebRTC浏览器: 运行中"
    else
        print_status "WARN" "WebRTC浏览器: 未运行"
    fi
    
    # 配置文件
    if [ -f "$CONFIG_FILE" ]; then
        print_status "OK" "推流配置: 存在"
    else
        print_status "ERROR" "推流配置: 不存在"
    fi
}

# 查看服务日志
show_logs() {
    print_status "TITLE" "服务日志"
    echo ""
    
    echo "选择要查看的日志："
    echo "1. 推流服务日志（最近50行）"
    echo "2. 推流服务实时日志"
    echo "3. 系统音频日志"
    echo "0. 返回主菜单"
    echo ""
    read -p "请选择 (0-3): " log_choice
    
    case $log_choice in
        1)
            if [ -f "$BASE_DIR/logs/multi-camera-push.log" ]; then
                echo ""
                print_status "INFO" "推流服务日志（最近50行）："
                echo ""
                tail -n 50 "$BASE_DIR/logs/multi-camera-push.log"
            else
                print_status "ERROR" "日志文件不存在"
            fi
            ;;
        2)
            if [ -f "$BASE_DIR/logs/multi-camera-push.log" ]; then
                print_status "INFO" "实时日志（按Ctrl+C退出）："
                echo ""
                tail -f "$BASE_DIR/logs/multi-camera-push.log"
            else
                print_status "ERROR" "日志文件不存在"
            fi
            ;;
        3)
            print_status "INFO" "系统音频日志："
            echo ""
            dmesg | grep -i audio | tail -n 20
            ;;
        0)
            return 0
            ;;
        *)
            print_status "ERROR" "无效选择"
            ;;
    esac
}

# 重启所有服务
restart_all_services() {
    print_status "TITLE" "重启所有服务"
    echo ""
    
    read -p "确认重启所有服务？(y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        print_status "INFO" "取消重启"
        return 0
    fi
    
    print_status "INFO" "重启PulseAudio..."
    pulseaudio --kill 2>/dev/null || true
    sleep 2
    pulseaudio --start --log-target=syslog 2>/dev/null || true
    
    print_status "INFO" "重启推流服务..."
    systemctl restart multi-camera-push 2>/dev/null || true
    sleep 3
    
    print_status "INFO" "重启WebRTC服务..."
    pkill chromium 2>/dev/null || true
    sleep 2
    if [ -f "/home/<USER>/srs/auto_request.sh" ]; then
        /home/<USER>/srs/auto_request.sh &
    fi
    
    print_status "OK" "所有服务重启完成"
}

# 主循环
main_loop() {
    while true; do
        show_main_menu
        read -p "请输入选择 (0-11): " choice
        echo ""
        
        case $choice in
            1) show_camera_status ;;
            2) add_camera ;;
            3) remove_camera ;;
            4) restart_streaming ;;
            5) quick_audio_check ;;
            6) test_audio ;;
            7) check_conflicts ;;
            8) fix_audio ;;
            9) show_system_status ;;
            10) show_logs ;;
            11) restart_all_services ;;
            0) 
                echo "退出系统维护工具"
                exit 0
                ;;
            *)
                print_status "ERROR" "无效选择，请输入 0-11"
                ;;
        esac
        
        wait_for_key
    done
}

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo运行此脚本: sudo $0"
    exit 1
fi

# 启动主循环
main_loop

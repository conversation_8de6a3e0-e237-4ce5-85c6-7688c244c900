package com.demo.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demo.entity.VideoRecording;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 视频录制服务接口
 */
public interface VideoRecordingService extends IService<VideoRecording> {

    /**
     * 处理SRS DVR回调
     * @param payload DVR回调数据
     * @return 处理结果
     */
    SaResult handleDvrCallback(Map<String, Object> payload);

    /**
     * 获取录制文件列表（使用数据库查询）
     */
    SaResult getRecordingsList(String streamName, LocalDateTime startTime, LocalDateTime endTime);

    SaResult getCameraTree();


}
